const DocumentService = require('../services/documentService');

class DocumentController {
  static async uploadDocument(req, res) {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }

      // Extract document metadata from request body
      const { description, document_type, is_public } = req.body;

      // Validate document_type if provided
      const validDocumentTypes = ['ID Proof', 'Experience Proof', 'Other Documents'];
      if (document_type && !validDocumentTypes.includes(document_type)) {
        return res.status(400).json({
          error: 'Invalid document_type. Must be one of: ID Proof, Experience Proof, Other Documents'
        });
      }

      // Upload document and save to database
      const document = await DocumentService.uploadDocument(req.user.sub, req.file, {
        description,
        document_type,
        is_public: is_public === 'true'
      });

      // Generate a signed URL for immediate access
      const signedUrl = await DocumentService.getDocumentUrl(document.s3_key);

      res.status(201).json({
        message: 'Document uploaded successfully to AWS S3',
        document,
        signed_url: signedUrl
      });
    } catch (error) {
      console.error('Upload error:', error);
      res.status(500).json({ error: `Failed to upload document: ${error.message}` });
    }
  }

  static async getDocumentUrl(req, res) {
    try {
      const { s3Key } = req.params;
      const url = await DocumentService.getDocumentUrl(s3Key);
      res.json({ url });
    } catch (error) {
      console.error('Get URL error:', error);
      res.status(500).json({ error: 'Failed to generate document URL' });
    }
  }

  static async deleteDocument(req, res) {
    try {
      const { s3Key } = req.params;
      await DocumentService.deleteDocument(s3Key);
      res.json({ message: 'Document deleted successfully' });
    } catch (error) {
      console.error('Delete error:', error);
      res.status(500).json({ error: 'Failed to delete document' });
    }
  }

  static async listDocuments(req, res) {
    try {
      const { page = 1, limit = 10, document_type } = req.query;

      const result = await DocumentService.listUserDocuments(req.user.sub, {
        page: parseInt(page),
        limit: parseInt(limit),
        documentType: document_type
      });

      res.json(result);
    } catch (error) {
      console.error('List error:', error);
      res.status(500).json({ error: 'Failed to list documents' });
    }
  }

  static async getDocumentById(req, res) {
    try {
      const { id } = req.params;
      const document = await DocumentService.getDocumentById(id);

      if (!document) {
        return res.status(404).json({ error: 'Document not found' });
      }

      // Check if the document belongs to the current user
      if (document.user_id !== req.user.sub) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Generate a signed URL for immediate access
      const signedUrl = await DocumentService.getDocumentUrl(document.s3_key);

      res.json({
        document,
        signed_url: signedUrl
      });
    } catch (error) {
      console.error('Get document error:', error);
      res.status(500).json({ error: 'Failed to get document' });
    }
  }

  static async updateDocument(req, res) {
    try {
      const { id } = req.params;
      const { description, document_type, is_public } = req.body;

      // First, check if the document exists and belongs to the user
      const existingDocument = await DocumentService.getDocumentById(id);

      if (!existingDocument) {
        return res.status(404).json({ error: 'Document not found' });
      }

      if (existingDocument.user_id !== req.user.sub) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Update the document
      const updatedDocument = await DocumentService.updateDocument(id, {
        description,
        document_type,
        is_public: is_public === 'true'
      });

      res.json({
        message: 'Document updated successfully',
        document: updatedDocument
      });
    } catch (error) {
      console.error('Update error:', error);
      res.status(500).json({ error: 'Failed to update document' });
    }
  }
}

module.exports = DocumentController;