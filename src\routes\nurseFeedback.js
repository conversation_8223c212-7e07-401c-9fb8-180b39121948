const express = require('express');
const { check } = require('express-validator');
const NurseFeedbackController = require('../controllers/nurseFeedbackController');
const { authenticateToken, requireUserType } = require('../middleware/auth');
const checkDatabaseConnection = require('../middleware/database');

const router = express.Router();

// Validation middleware for feedback submission
const validateFeedback = [
  check('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  check('name')
    .notEmpty()
    .withMessage('Name is required')
    .isLength({ max: 255 })
    .withMessage('Name cannot exceed 255 characters'),
  check('recipientName')
    .notEmpty()
    .withMessage('Recipient name is required')
    .isLength({ max: 255 })
    .withMessage('Recipient name cannot exceed 255 characters'),
  check('comments')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Comments cannot exceed 1000 characters'),
  check('nurseId')
    .notEmpty()
    .withMessage('Nurse ID is required')
];

const requireNurseOrCustomer = (req, res, next) => {
  if (req.user && (req.user.userType === 'nurse' || req.user.userType === 'customer')) {
    return next();
  }
  return res.status(403).json({ 
    error: 'Access denied: Nurse or Customer access required',
    userType: req.user ? req.user.userType : 'unknown'
  });
};

router.use(checkDatabaseConnection);

// Get user details for feedback form
router.get('/user/:nurseId',authenticateToken,requireUserType,requireNurseOrCustomer, NurseFeedbackController.getUserDetailsForFeedback);

// Submit feedback for a nurse
router.post('/submitRating', validateFeedback, NurseFeedbackController.submitFeedback);

// Get feedback for a nurse
router.get('/:nurseId',authenticateToken,requireUserType,requireNurseOrCustomer, NurseFeedbackController.getNurseFeedback);

module.exports = router; 