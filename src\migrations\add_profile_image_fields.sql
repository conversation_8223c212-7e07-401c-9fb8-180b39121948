-- Add profile image fields to users table
-- Migration: add_profile_image_fields.sql
-- Purpose: Add columns to store profile image metadata in users table

ALTER TABLE users
ADD COLUMN profile_image_name VARCHAR(255) NULL,
ADD COLUMN s3_key VARCHAR(255) NULL,
ADD COLUMN s3_url VARCHAR(512) NULL;

-- Rollback instructions (if needed):
-- ALTER TABLE users
-- DROP COLUMN profile_image_name,
-- DROP COLUMN s3_key,
-- DROP COLUMN s3_url;
