const AvailabilityService = require("../services/availabilityService");
const UserService = require("../services/userService");

class AvailabilityController {
  // --- USER OPERATIONS (nurseId from token ) ---

  static async createMyAvailability(req, res) {
    try {
      const { availability_slot, hourly_fare } = req.body;

      // Get nurseId from authenticated token (using sub from Cognito)
      const nurseId = req.user.sub;

      if (!nurseId) {
        return res.status(400).json({ error: "Unable to extract nurse ID from token" });
      }

      if (!availability_slot) {
        return res.status(400).json({ error: "Availability slot is required" });
      }

      if (!hourly_fare) {
        return res.status(400).json({ error: "Hourly fare is required" });
      }

      const availability = await AvailabilityService.createAvailability(
        nurseId,
        {
          availability_slot,
          hourly_fare,
        }
      );

      res.status(201).json({
        message: "Availability created successfully",
        availability,
      });
    } catch (error) {
      console.error("Controller error:", error);
      if (
        error.message.includes("must be in the future") ||
        error.message.includes("already exists") ||
        error.message.includes("must be greater than 0") ||
        error.message.includes("User not found")
      ) {
        res.status(400).json({ error: error.message });
      } else {
        res.status(500).json({ error: "Failed to create availability" });
      }
    }
  }

  // NEW: Create multiple availability slots at once
  static async createMultipleAvailabilities(req, res) {
    try {
      const { availability_slots } = req.body;
      const nurseId = req.user.sub;
      if (!nurseId) {
        return res.status(400).json({ error: "Unable to extract nurse ID from token" });
      }
      if (!availability_slots || !Array.isArray(availability_slots) || availability_slots.length === 0) {
        return res.status(400).json({
          error: "availability_slots is required and must be a non-empty array"
        });
      }
      // Validate each slot has required fields
      for (let i = 0; i < availability_slots.length; i++) {
        const slot = availability_slots[i];
        if (!slot.availability_date) {
          return res.status(400).json({
            error: `Availability date is required for item ${i + 1}`
          });
        }
        if (!slot.availability_timeSlots || !Array.isArray(slot.availability_timeSlots) || slot.availability_timeSlots.length === 0) {
          return res.status(400).json({
            error: `Availability timeSlots must be a non-empty array for item ${i + 1}`
          });
        }
        if (!slot.hourly_fare) {
          return res.status(400).json({
            error: `Hourly fare is required for item ${i + 1}`
          });
        }
      }
      const result = await AvailabilityService.createMultipleAvailabilities(
        nurseId,
        availability_slots
      );
      res.status(201).json({
        message: "Multiple availabilities created successfully",
        summary: {
          total_requested: availability_slots.length,
          successful_dates: result.successful.length,
          failed_dates: result.failed.length,
        },
        successful: result.successful,
        failed: result.failed
      });
    } catch (error) {
      console.error("Controller error:", error);
      if (error.message.includes("User not found")) {
        res.status(400).json({ error: error.message });
      } else {
        res.status(500).json({ error: "Failed to create multiple availabilities" });
      }
    }
  }

  static async getMyAvailabilities(req, res) {
    try {
      const nurseId = req.user.sub;
      const { date } = req.query;
      const result = await AvailabilityService.getNurseAvailability(nurseId, date);
      res.json(result);
    } catch (error) {
      console.error("Controller error:", error);
      if (error.message === 'User not found') {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: "Failed to fetch my availabilities" });
      }
    }
  }

  static async updateMyAvailability(req, res) {
    try {
      const { id } = req.params;
      const { availability_slot, hourly_fare } = req.body;

      if (!id) {
        return res.status(400).json({ error: "Availability id is required" });
      }

      const updatedAvailability = await AvailabilityService.updateAvailability(
        id,
        {
          availability_slot,
          hourly_fare,
        }
      );

      res.json({
        message: "Availability updated successfully",
        availability: updatedAvailability,
      });
    } catch (error) {
      console.error("Controller error:", error);
      if (error.message.includes("not found")) {
        res.status(404).json({ error: error.message });
      } else if (
        error.message.includes("must be in the future") ||
        error.message.includes("must be greater than 0")
      ) {
        res.status(400).json({ error: error.message });
      } else {
        res.status(500).json({ error: "Failed to update availability" });
      }
    }
  }

  static async deleteMyAvailability(req, res) {
    try {
      const { id } = req.params;

      if (!id) {
        return res.status(400).json({ error: "Availability ID is required" });
      }

      await AvailabilityService.deleteAvailability(id);
      res.json({
        message: "Availability deleted successfully",
      });
    } catch (error) {
      console.error("Controller error:", error);
      if (error.message.includes("not found")) {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: "Failed to delete availability" });
      }
    }
  }

  static async deleteAllMyAvailabilities(req, res) {
    try {
      // Get nurseId from authenticated token
      const nurseId = req.user.sub;

      await AvailabilityService.deleteAllNurseAvailabilities(nurseId);
      res.json({
        message: "All availabilities deleted successfully",
      });
    } catch (error) {
      console.error("Controller error:", error);
      if (error.message.includes("not found")) {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: "Failed to delete availabilities" });
      }
    }
  }

  // === PUBLIC/ADMIN OPERATIONS (nurseId as parameter) ===

  static async getNurseAvailability(req, res) {
    try {
      const nurseId = req.params.nurseId;
      const { date } = req.query;

      const availability = await AvailabilityService.getNurseAvailability(
        nurseId,
        date
      );
      res.json(availability);
    } catch (error) {
      console.error("Controller error:", error);
      if (error.message.includes("not found")) {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: "Failed to fetch nurse availability" });
      }
    }
  }

  static async getAllAvailabilities(req, res) {
    try {
      const { page = 1, limit = 10, date, nurseId } = req.query;
      const filters = { date, nurseId };

      const result = await AvailabilityService.getAllAvailabilities(
        parseInt(page),
        parseInt(limit),
        filters
      );

      res.json({
        availabilities: result.data,
        pagination: result.pagination,
        message: "Availabilities retrieved successfully",
      });
    } catch (error) {
      console.error("Controller error:", error);
      res.status(500).json({ error: "Failed to fetch availabilities" });
    }
  }

  static async getUserDetailsForAvailability(req, res) {
    try {
      const { nurseId } = req.params;

      // Get user details using the same method as feedback API
      const user = await UserService.findByCognitoId(nurseId);
      if (!user) {
        return res.status(404).json({ error: "User not found" });
      }

      // Return user details needed for the form
      res.json({
        name: user.given_name || user.name,
        nurseId: user.cognito_id,
      });
    } catch (error) {
      console.error("Controller error:", error);
      res.status(500).json({ error: "Failed to fetch user details" });
    }
  }
}

module.exports = AvailabilityController;