const TimeSlots = require('../models/timeSlots')

class TimeSlotsService {
  static async getAllTimeSlots() {
    try {
      const slots = await TimeSlots.findAll();
      return slots;
    } catch (error) {
      console.error('Service error in getAllTimeSlots:', error);
      throw error;
    }
  }

  static async getTimeSlotById(id) {
    try {
      const slot = await TimeSlots.findById(id);
      if (!slot) {
        throw new Error('Time slot not found');
      }
      return slot;
    } catch (error) {
      console.error('Service error in getTimeSlotById:', error);
      throw error;
    }
  }
}

module.exports = TimeSlotsService;