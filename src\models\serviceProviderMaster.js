const { pool } = require('../config/database');

class ServiceProviderMaster {
  constructor(data) {
    this.id = data.id;
    this.service_name = data.service_name;
    this.iaActive = data.iaActive;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  static async findAll() {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM service_provider_master WHERE iaActive = "1"'
      );
      return rows.map(row => new ServiceProviderMaster(row));
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch service providers');
    }
  }

  static async findById(id) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM service_provider_master WHERE id = ? AND status = "active"',
        [id]
      );
      return rows.length ? new ServiceProviderMaster(rows[0]) : null;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch service provider');
    }
  }
}

module.exports = ServiceProviderMaster; 