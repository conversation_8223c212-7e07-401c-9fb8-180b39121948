const NurseAvailability = require('../models/availabilityModel');
const UserService = require('../services/userService');

class AvailabilityService {
  // Helper function to convert ISO date to MySQL datetime format
  static formatDateForMySQL(isoDateString) {
    const date = new Date(isoDateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  static async createAvailability(nurseId, availabilityData) {
    try {
      // Verify user exists
      const user = await UserService.findByCognitoId(nurseId);
      if (!user) {
        throw new Error('User not found');
      }

      // Convert ISO date to MySQL format
      const formattedAvailabilitySlot = this.formatDateForMySQL(availabilityData.availability_slot);

      // Validate date is in future
      const slotDate = new Date(availabilityData.availability_slot);
      if (slotDate <= new Date()) {
        throw new Error('Availability slot must be in the future');
      }

      // Validate hourly fare
      if (availabilityData.hourly_fare <= 0) {
        throw new Error('Hourly fare must be greater than 0');
      }

      // Check if this EXACT same slot already exists (optional - prevents exact duplicates)
      const existingExactSlot = await NurseAvailability.findExactSlot(nurseId, formattedAvailabilitySlot);
      if (existingExactSlot) {
        throw new Error('This exact availability slot already exists. Please choose a different time.');
      }

      const availability = await NurseAvailability.create({
        nurse_id: nurseId,
        availability_slot: formattedAvailabilitySlot,
        hourly_fare: availabilityData.hourly_fare
      });

      return availability;
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  // NEW: Create multiple availability slots 
  static async createMultipleAvailabilities(nurseId, availabilitySlots) {
    try {
      const user = await UserService.findByCognitoId(nurseId);
      if (!user) {
        throw new Error('User not found');
      }
      const results = {
        successful: [],
        failed: []
      };
      // Group availability slots by date
      const groupedByDate = {};
      for (let i = 0; i < availabilitySlots.length; i++) {
        const slotGroup = availabilitySlots[i];
        const { availability_date, availability_timeSlots, hourly_fare } = slotGroup;
        // Group by date
        if (!groupedByDate[availability_date]) {
          groupedByDate[availability_date] = {
            timeSlots: [],
            hourly_fare: hourly_fare,
            group_index: i
          };
        }
        // Add time slots to the group
        groupedByDate[availability_date].timeSlots.push(...availability_timeSlots);
        // Remove duplicates within the same date
        groupedByDate[availability_date].timeSlots = [...new Set(groupedByDate[availability_date].timeSlots)];
      }
      // Process each grouped date
      for (const [date, dateGroup] of Object.entries(groupedByDate)) {
        try {
          // Check if availability already exists for this date
          const existingAvailability = await NurseAvailability.findByDateAndNurse(nurseId, date);
          if (existingAvailability) {
            // Update existing availability by merging time slots
            let existingTimeSlots = [];
            // Safely parse existing time slots with detailed logging
            if (existingAvailability.availability_slots) {
              if (Array.isArray(existingAvailability.availability_slots)) {
                // Already parsed by constructor
                existingTimeSlots = existingAvailability.availability_slots;
              } else if (typeof existingAvailability.availability_slots === 'string') {
                const rawString = existingAvailability.availability_slots;
                try {
                  existingTimeSlots = JSON.parse(rawString);
                } catch (parseError) {
                  // Legacy single time slot string - convert to array
                  existingTimeSlots = [rawString.trim()]; // Trim any whitespace
                  // Update the database record to new format immediately
                  await NurseAvailability.updateTimeSlots(
                    existingAvailability.id,
                    existingTimeSlots,
                    existingAvailability.hourly_fare
                  );
                }
              }
            }
            const mergedTimeSlots = [...new Set([...existingTimeSlots, ...dateGroup.timeSlots])];
            const updatedAvailability = await NurseAvailability.updateTimeSlots(
              existingAvailability.id,
              mergedTimeSlots,
              dateGroup.hourly_fare
            );
            results.successful.push({
              group_index: dateGroup.group_index,
              availability_date: date,
              availability_time_slots: mergedTimeSlots,
              hourly_fare: dateGroup.hourly_fare,
              action: 'updated',
              updated_availability: updatedAvailability
            });
          } else {
            // Create new availability with all time slots for this date
            const availability = await NurseAvailability.create({
              nurse_id: nurseId,
              availability_date: date,
              availability_time: JSON.stringify(dateGroup.timeSlots), // Store as JSON array
              hourly_fare: dateGroup.hourly_fare
            });
            results.successful.push({
              group_index: dateGroup.group_index,
              availability_date: date,
              availability_time_slots: dateGroup.timeSlots,
              hourly_fare: dateGroup.hourly_fare,
              action: 'created',
              created_availability: availability
            });
          }
        } catch (error) {
          console.error(`Error processing date ${date}:`, error);
          results.failed.push({
            group_index: dateGroup.group_index,
            availability_date: date,
            availability_time_slots: dateGroup.timeSlots,
            hourly_fare: dateGroup.hourly_fare,
            error: error.message
          });
        }
      }
      return results;
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  // NEW: Batch create with transaction (all or nothing approach)
  static async createMultipleAvailabilitiesTransaction(nurseId, availabilitySlots) {
    try {
      // Verify user exists
      const user = await UserService.findByCognitoId(nurseId);
      if (!user) {
        throw new Error('User not found');
      }

      // Validate all slots first
      const validatedSlots = [];
      for (let i = 0; i < availabilitySlots.length; i++) {
        const slotData = availabilitySlots[i];

        // Validate date is in future
        const slotDate = new Date(slotData.availability_slot);
        if (slotDate <= new Date()) {
          throw new Error(`Slot ${i + 1}: Availability slot must be in the future`);
        }

        // Validate hourly fare
        if (slotData.hourly_fare <= 0) {
          throw new Error(`Slot ${i + 1}: Hourly fare must be greater than 0`);
        }

        // Convert ISO date to MySQL format
        const formattedAvailabilitySlot = this.formatDateForMySQL(slotData.availability_slot);

        // Check if this EXACT same slot already exists
        const existingExactSlot = await NurseAvailability.findExactSlot(nurseId, formattedAvailabilitySlot);
        if (existingExactSlot) {
          throw new Error(`Slot ${i + 1}: This exact availability slot already exists`);
        }

        validatedSlots.push({
          nurse_id: nurseId,
          availability_slot: formattedAvailabilitySlot,
          hourly_fare: slotData.hourly_fare
        });
      }

      // Create all slots using batch insert
      const createdAvailabilities = await NurseAvailability.createMultiple(validatedSlots);

      return {
        successful: createdAvailabilities,
        failed: [],
        total: createdAvailabilities.length
      };
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  static async getNurseAvailability(nurseId, date = null) {
    try {
      const user = await UserService.findByCognitoId(nurseId);
      if (!user) {
        throw new Error('User not found');
      }
      const availabilitySlots = await NurseAvailability.findByNurseId(nurseId, date);
      const formattedResult = {
        nurse_id: nurseId,
        nurseName: user.given_name || user.name,
        availability_slots: availabilitySlots.map(slot => {
          return {
            id: slot.id,
            availability_date: slot.availability_date,
            availability_slots: slot.availability_slots,
            hourly_fare: slot.hourly_fare,
          };
        }),
      };
      return formattedResult;
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  static async updateAvailability(id, updateData) {
    try {
      // Find the availability by nurseId first
      const existingAvailability = await NurseAvailability.findById(id);
      if (!existingAvailability) {
        throw new Error('Availability not found');
      }

      // Validate and format date if provided
      if (updateData.availability_slot) {
        const slotDate = new Date(updateData.availability_slot);
        if (slotDate <= new Date()) {
          throw new Error('Availability slot must be in the future');
        }
        // Format the date for MySQL
        updateData.availability_slot = this.formatDateForMySQL(updateData.availability_slot);
      }

      // Validate hourly fare if provided
      if (updateData.hourly_fare && updateData.hourly_fare <= 0) {
        throw new Error('Hourly fare must be greater than 0');
      }

      const updatedAvailability = await NurseAvailability.update(id, updateData);
      return updatedAvailability;
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  static async deleteAvailability(id) {
    try {
      const existingAvailabilities = await NurseAvailability.findById(id);
      if (!existingAvailabilities || existingAvailabilities.length === 0) {
        throw new Error('No availabilities found for this ID');
      }

      await NurseAvailability.deleteById(id);
      return true;
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  static async deleteAllNurseAvailabilities(nurseId) {
    try {
      // Verify user exists
      const user = await UserService.findByCognitoId(nurseId);
      if (!user) {
        throw new Error('User not found');
      }

      const existingAvailabilities = await NurseAvailability.findByNurseId(nurseId);
      if (!existingAvailabilities || existingAvailabilities.length === 0) {
        throw new Error('No availabilities found for this nurse');
      }

      await NurseAvailability.deleteByNurseId(nurseId);
      return true;
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  static async getAllAvailabilities(page = 1, limit = 10, filters = {}) {
    try {
      const offset = (page - 1) * limit;
      const { data, total } = await NurseAvailability.findAll(limit, offset, filters);

      return {
        data,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(total / limit),
          totalRecords: total,
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      };
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }
}

module.exports = AvailabilityService;