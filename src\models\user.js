const pool = require('../config/database');

class User {
  constructor(data) {
    this.id = data.id;
    this.cognito_id = data.cognito_id;
    this.username = data.username;
    this.email = data.email;
    this.name = data.name;
    this.middle_name = data.middle_name;
    this.family_name = data.family_name;
    this.given_name = data.given_name;
    this.phone_number = data.phone_number;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
    this.is_confirmed = data.is_confirmed || false;
    // Location fields
    this.latitude = data.latitude;
    this.longitude = data.longitude;
    this.address = data.address;
    this.city = data.city;
    this.state = data.state;
    this.country = data.country;
    this.postal_code = data.postal_code;
    this.nurse_onboard_complete = data.nurse_onboard_complete;
    this.nurse_set_location = data.nurse_set_location;
  }

  static async findByUsername(username) {
    try {
      const [rows] = await pool.query('SELECT * FROM users WHERE username = ?', [username]);
      return rows.length ? new User(rows[0]) : null;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch user by username');
    }
  }

  static async findByEmail(email) {
    try {
      const [rows] = await pool.query('SELECT * FROM users WHERE email = ?', [email]);
      return rows.length ? new User(rows[0]) : null;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch user by email');
    }
  }

  static async create(userData) {
    try {
      const now = new Date();
      const [result] = await pool.query(
        'INSERT INTO users (cognito_id, username, email, name, phone_number, middle_name, family_name, given_name, created_at, updated_at, is_confirmed) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [
          userData.cognito_id,
          userData.username,
          userData.email,
          userData.name || null,
          userData.phone_number || null,
          userData.middle_name || null,
          userData.family_name || null,
          userData.given_name || null,
          now,
          now,
          userData.is_confirmed || false,
          userData.nurse_onboard_complete || false,
          userData.nurse_set_location || false
        ]
      );

      return { id: result.insertId, ...userData };
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to create user in database');
    }
  }

  static async updateConfirmationStatus(username, status) {
    try {
      await pool.query(
        'UPDATE users SET is_confirmed = ?, updated_at = ? WHERE username = ?',
        [status, new Date(), username]
      );
      return true;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to update user confirmation status');
    }
  }
}

module.exports = User;