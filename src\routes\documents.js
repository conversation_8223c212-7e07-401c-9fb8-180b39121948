const express = require('express');
const multer = require('multer');
const { authenticateToken, requireUserType } = require('../middleware/auth');
const DocumentController = require('../controllers/documentController');

const router = express.Router();
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Middleware to check for nurse user type only
const requireNurse = (req, res, next) => {
  if (req.user && req.user.userType === 'nurse') {
    return next();
  }
  return res.status(403).json({ 
    error: 'Access denied: Nurse access required',
    userType: req.user ? req.user.userType : 'unknown'
  });
};

// All routes require authentication and nurse access
router.use(authenticateToken);
router.use(requireUserType);
router.use(requireNurse);

// Upload a document - Nurse only
router.post('/upload', upload.single('file'), DocumentController.uploadDocument);

// Get a signed URL for downloading a document by S3 key - Nurse only
router.get('/s3/:s3Key/url', DocumentController.getDocumentUrl);

// Delete a document by S3 key - Nurse only
router.delete('/s3/:s3Key', DocumentController.deleteDocument);

// Get document by ID - Nurse only
router.get('/:id', DocumentController.getDocumentById);

// Update document metadata - Nurse only
router.put('/:id', DocumentController.updateDocument);

// List all documents for the current user - Nurse only
router.get('/', DocumentController.listDocuments);

module.exports = router;