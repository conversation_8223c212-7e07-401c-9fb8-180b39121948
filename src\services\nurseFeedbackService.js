const NurseFeedback = require('../models/nurseFeedback');

class NurseFeedbackService {
  static async submitFeedback(nurseId, feedbackData) {
    try {
      // Validate rating
      if (feedbackData.rating < 1 || feedbackData.rating > 5) {
        throw new Error('Rating must be between 1 and 5');
      }

      const feedback = await NurseFeedback.create({
        nurseId: nurseId,
        rating: feedbackData.rating,
        name: feedbackData.name,
        recipientName: feedbackData.recipientName,
        comments: feedbackData.comments,
        feedbackId: feedbackData.feedbackId
      });

      return feedback;
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  static async getNurseFeedback(nurseId) {
    try {
      const feedback = await NurseFeedback.findByNurseId(nurseId);
      const ratingStats = await NurseFeedback.getAverageRating(nurseId);

      return {
        feedback,
        rating_stats: ratingStats
      };
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }
}

module.exports = NurseFeedbackService; 