const AWS = require('aws-sdk');
require('dotenv').config();

async function updateCognitoAppClient() {
  // Configure AWS
  AWS.config.update({
    region: process.env.AWS_REGION,
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  });

  // Initialize Cognito Identity Provider
  const cognitoIdentityServiceProvider = new AWS.CognitoIdentityServiceProvider();

  try {
    // First, get the current app client settings
    const listClientsParams = {
      UserPoolId: process.env.NURSE_COGNITO_USER_POOL_ID,
      MaxResults: 60
    };

    const clients = await cognitoIdentityServiceProvider.listUserPoolClients(listClientsParams).promise();
    
    if (!clients.UserPoolClients || clients.UserPoolClients.length === 0) {
      console.log('No app clients found in the user pool');
      return;
    }

    // Find the client with the matching client ID
    const targetClientId = process.env.NURSE_COGNITO_APP_CLIENT_ID;
    const targetClient = clients.UserPoolClients.find(client => client.ClientId === targetClientId);
    
    if (!targetClient) {
      console.log(`App client with ID ${targetClientId} not found`);
      return;
    }

    console.log(`Found app client: ${targetClient.ClientName}`);

    // Get detailed client information
    const describeParams = {
      UserPoolId: process.env.NURSE_COGNITO_USER_POOL_ID,
      ClientId: targetClientId
    };

    const clientDetails = await cognitoIdentityServiceProvider.describeUserPoolClient(describeParams).promise();
    const client = clientDetails.UserPoolClient;

    console.log('Current ExplicitAuthFlows:', client.ExplicitAuthFlows);

    // Check if USER_PASSWORD_AUTH is already enabled
    if (client.ExplicitAuthFlows && client.ExplicitAuthFlows.includes('USER_PASSWORD_AUTH')) {
      console.log('USER_PASSWORD_AUTH flow is already enabled');
      return;
    }

    // Update the client to enable USER_PASSWORD_AUTH
    const updateParams = {
      UserPoolId: process.env.NURSE_COGNITO_USER_POOL_ID,
      ClientId: targetClientId,
      ClientName: client.ClientName,
      RefreshTokenValidity: client.RefreshTokenValidity,
      AccessTokenValidity: client.AccessTokenValidity,
      IdTokenValidity: client.IdTokenValidity,
      TokenValidityUnits: client.TokenValidityUnits,
      ReadAttributes: client.ReadAttributes,
      WriteAttributes: client.WriteAttributes,
      ExplicitAuthFlows: [
        ...(client.ExplicitAuthFlows || []),
        'ALLOW_USER_PASSWORD_AUTH',
        'ALLOW_ADMIN_USER_PASSWORD_AUTH'
      ],
      SupportedIdentityProviders: client.SupportedIdentityProviders,
      CallbackURLs: client.CallbackURLs,
      LogoutURLs: client.LogoutURLs,
      AllowedOAuthFlows: client.AllowedOAuthFlows,
      AllowedOAuthScopes: client.AllowedOAuthScopes,
      AllowedOAuthFlowsUserPoolClient: client.AllowedOAuthFlowsUserPoolClient,
      PreventUserExistenceErrors: client.PreventUserExistenceErrors
    };

    await cognitoIdentityServiceProvider.updateUserPoolClient(updateParams).promise();
    console.log('Successfully updated app client to enable USER_PASSWORD_AUTH flow');

  } catch (error) {
    console.error('Error updating Cognito app client:', error);
  }
}

updateCognitoAppClient();
