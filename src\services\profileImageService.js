const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');
const ProfileImage = require('../models/profileImage');
require('dotenv').config();

// Configure AWS
AWS.config.update({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

const s3 = new AWS.S3();
const BUCKET_NAME = process.env.AWS_S3_PROFILE_BUCKET_NAME;

class ProfileImageService {
  static validateImageFile(file) {
    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      throw new Error('File size exceeds 5MB limit');
    }

    // Check file type (only images)
    const allowedMimeTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/bmp',
      'image/svg+xml'
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new Error('Invalid file type. Only image files are allowed (JPEG, PNG, GIF, WebP, BMP, SVG)');
    }

    return true;
  }

  static async uploadProfileImage(userId, file) {
    try {
      // Validate the image file
      this.validateImageFile(file);

      // Check if user already has a profile image and delete it
      const existingImage = await ProfileImage.findByUserId(userId);
      if (existingImage && existingImage.s3_key) {
        await this.deleteFromS3(existingImage.s3_key);
      }

      const fileExtension = file.originalname.split('.').pop();
      const fileName = `profile-images/${userId}/${uuidv4()}.${fileExtension}`;

      const params = {
        Bucket: BUCKET_NAME,
        Key: fileName,
        Body: file.buffer,
        ContentType: file.mimetype,
        ACL: 'private'
      };

      const uploadResult = await s3.upload(params).promise();

      // Update user record in database
      const imageInfo = {
        profile_image_name: file.originalname,
        s3_key: fileName,
        s3_url: uploadResult.Location
      };

      const updatedUser = await ProfileImage.updateProfileImage(userId, imageInfo);

      return updatedUser;
    } catch (error) {
      console.error('S3 upload error:', error);
      throw new Error(`Failed to upload profile image: ${error.message}`);
    }
  }

  static async getProfileImageUrl(s3Key) {
    try {
      const params = {
        Bucket: BUCKET_NAME,
        Key: s3Key,
        Expires: 3600 // URL expires in 1 hour
      };

      const url = await s3.getSignedUrlPromise('getObject', params);
      return url;
    } catch (error) {
      console.error('S3 get URL error:', error);
      throw new Error('Failed to generate profile image URL');
    }
  }

  static async deleteFromS3(s3Key) {
    try {
      const params = {
        Bucket: BUCKET_NAME,
        Key: s3Key
      };

      await s3.deleteObject(params).promise();
      return true;
    } catch (error) {
      console.error('S3 delete error:', error);
      throw new Error('Failed to delete profile image from S3');
    }
  }

  static async deleteProfileImage(userId) {
    try {
      // Get user's current profile image
      const user = await ProfileImage.findByUserId(userId);
      
      if (!user || !user.s3_key) {
        throw new Error('No profile image found for this user');
      }

      // Delete from S3
      await this.deleteFromS3(user.s3_key);

      // Delete from database
      await ProfileImage.deleteProfileImage(userId);

      return true;
    } catch (error) {
      console.error('Delete profile image error:', error);
      throw new Error(`Failed to delete profile image: ${error.message}`);
    }
  }

  static async getProfileImageByUserId(userId) {
    try {
      const user = await ProfileImage.findByUserId(userId);
      
      if (!user || !user.s3_key) {
        return null;
      }

      // Generate signed URL for the image
      const signedUrl = await this.getProfileImageUrl(user.s3_key);

      return {
        profile_image_name: user.profile_image_name,
        s3_key: user.s3_key,
        s3_url: user.s3_url,
        signed_url: signedUrl,
        updated_at: user.updated_at
      };
    } catch (error) {
      console.error('Get profile image error:', error);
      throw new Error('Failed to get profile image');
    }
  }

  static async updateProfileImage(userId, file) {
    try {
      // This is essentially the same as upload since we replace the existing image
      return await this.uploadProfileImage(userId, file);
    } catch (error) {
      console.error('Update profile image error:', error);
      throw new Error(`Failed to update profile image: ${error.message}`);
    }
  }
}

module.exports = ProfileImageService;
