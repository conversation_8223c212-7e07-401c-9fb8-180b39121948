const jwt = require('jsonwebtoken');
const jwksClient = require('jwks-rsa');
const { promisify } = require('util');
require('dotenv').config();

// Configure JWKS clients for both Cognito user pools
const customerUserPoolConfig = {
  jwksUri: `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.CUSTOMER_COGNITO_USER_POOL_ID}/.well-known/jwks.json`,
  cache: true,
  rateLimit: true,
  jwksRequestsPerMinute: 5,
  handleSigningKeyError: (err, cb) => {
    console.error('Customer JWKS Error:', err);
    if (process.env.NODE_ENV === 'development' && process.env.USE_TEST_AUTH === 'true') {
      return cb(null, null);
    }
    return cb(err);
  }
};

const nurseUserPoolConfig = {
  jwksUri: `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.NURSE_COGNITO_USER_POOL_ID}/.well-known/jwks.json`,
  cache: true,
  rateLimit: true,
  jwksRequestsPerMinute: 5,
  handleSigningKeyError: (err, cb) => {
    console.error('Nurse JWKS Error:', err);
    if (process.env.NODE_ENV === 'development' && process.env.USE_TEST_AUTH === 'true') {
      return cb(null, null);
    }
    return cb(err);
  }
};

const customerJwksClient = jwksClient(customerUserPoolConfig);
const nurseJwksClient = jwksClient(nurseUserPoolConfig);

// Promisify the getSigningKey functions
const getCustomerSigningKey = promisify(customerJwksClient.getSigningKey);
const getNurseSigningKey = promisify(nurseJwksClient.getSigningKey);

// Configuration for both user pools
const USER_POOLS = {
  customer: {
    poolId: process.env.CUSTOMER_COGNITO_USER_POOL_ID,
    clientId: process.env.CUSTOMER_COGNITO_APP_CLIENT_ID,
    issuer: `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.CUSTOMER_COGNITO_USER_POOL_ID}`,
    getSigningKey: getCustomerSigningKey,
    userType: 'customer'
  },
  nurse: {
    poolId: process.env.NURSE_COGNITO_USER_POOL_ID,
    clientId: process.env.NURSE_COGNITO_APP_CLIENT_ID,
    issuer: `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.NURSE_COGNITO_USER_POOL_ID}`,
    getSigningKey: getNurseSigningKey,
    userType: 'nurse'
  }
};

// Helper function to try token verification with a specific user pool
const tryVerifyWithPool = async (token, poolConfig, kid) => {
  try {
    // Get the signing key from the specific pool's JWKS
    const key = await poolConfig.getSigningKey(kid);
    if (!key) {
      throw new Error(`No signing key found for kid: ${kid} in ${poolConfig.userType} pool`);
    }

    // Get the public key
    const signingKey = key.getPublicKey ? key.getPublicKey() : key.rsaPublicKey;
    if (!signingKey) {
      throw new Error(`Failed to get public key from signing key for ${poolConfig.userType} pool`);
    }

    // Verify the token
    const verifiedToken = jwt.verify(token, signingKey, {
      algorithms: ['RS256'],
      issuer: poolConfig.issuer,
      audience: poolConfig.clientId
    });

    return { verifiedToken, userType: poolConfig.userType };
  } catch (error) {
    throw error;
  }
};

const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: 'No authorization header' });
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }

    // For development or testing: verify mock tokens with test secret
    if (process.env.NODE_ENV === 'development' && process.env.USE_TEST_AUTH === 'true') {
      try {
        const decoded = jwt.verify(token, 'test-secret');
        req.user = {
          sub: decoded.sub,
          email: decoded.email,
          username: decoded.preferred_username,
          userType: decoded.userType || 'nurse' // Default to nurse for test tokens
        };
        return next();
      } catch (err) {
        console.error('Development token verification error:', err);
        return res.status(401).json({ error: 'Invalid development token' });
      }
    }

    // For production: use Cognito verification with multiple pools
    try {
      // Decode the token to get the kid (Key ID) and issuer
      const decodedToken = jwt.decode(token, { complete: true });
      if (!decodedToken) {
        return res.status(401).json({ error: 'Invalid token format' });
      }

      const kid = decodedToken.header.kid;
      const tokenIssuer = decodedToken.payload.iss;

      let verificationResult = null;
      let lastError = null;

      // Try to identify the correct user pool based on the issuer first
      let poolsToTry = Object.values(USER_POOLS);

      // If we can identify the issuer, try that pool first
      const matchingPool = Object.values(USER_POOLS).find(pool => pool.issuer === tokenIssuer);
      if (matchingPool) {
        poolsToTry = [matchingPool, ...poolsToTry.filter(p => p !== matchingPool)];
      }

      // Try each user pool until one succeeds
      for (const poolConfig of poolsToTry) {
        try {
          verificationResult = await tryVerifyWithPool(token, poolConfig, kid);
          break; // Success, exit the loop
        } catch (error) {
          lastError = error;
        }
      }

      if (!verificationResult) {
        console.error('Token verification failed with all user pools');
        console.error('Last error:', lastError);

        // If in development mode, fall back to test verification
        if (process.env.NODE_ENV === 'development' && process.env.USE_TEST_AUTH === 'true') {
          try {
            const decoded = jwt.verify(token, 'test-secret');
            req.user = {
              sub: decoded.sub,
              email: decoded.email,
              username: decoded.preferred_username,
              userType: decoded.userType || 'nurse'
            };
            return next();
          } catch (testVerifyError) {
            console.error('Test token verification error:', testVerifyError);
            return res.status(401).json({ error: 'Invalid token' });
          }
        } else {
          return res.status(401).json({
            error: 'Token verification failed with all user pools',
            details: lastError ? lastError.message : 'Unknown error',
            userPools: Object.keys(USER_POOLS)
          });
        }
      }

      // Set user information in the request object
      const { verifiedToken, userType } = verificationResult;
      req.user = {
        sub: verifiedToken.sub,
        email: verifiedToken.email,
        username: verifiedToken['cognito:username'] || verifiedToken.preferred_username,
        userType: userType,
        // Add additional fields that might be useful
        tokenUse: verifiedToken.token_use,
        clientId: verifiedToken.client_id || verifiedToken.aud
      };

      return next();
    } catch (err) {
      console.error('Token verification error:', err);
      return res.status(401).json({ error: 'Invalid or expired token' });
    }
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(500).json({ error: 'Authentication failed' });
  }
};

const requireUserType = (req, res, next) => {
  if (req.user && (req.user.userType === 'customer' || req.user.userType === 'nurse')) {
    return next();
  }
  return res.status(403).json({ error: 'Access denied: Valid user required' });
};

module.exports = { authenticateToken, requireUserType };