const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');
const Document = require('../models/document');
require('dotenv').config();

// Configure AWS
AWS.config.update({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

const s3 = new AWS.S3();
const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME;

class DocumentService {
  static async uploadDocument(userId, file, documentData = {}) {
    try {
      const fileExtension = file.originalname.split('.').pop();
      const fileName = `${userId}/${uuidv4()}.${fileExtension}`;

      const params = {
        Bucket: BUCKET_NAME,
        Key: fileName,
        Body: file.buffer,
        ContentType: file.mimetype,
        ACL: 'private'
      };

      const uploadResult = await s3.upload(params).promise();

      // Create document record in database
      const documentInfo = {
        user_id: userId,
        file_name: fileName,
        original_name: file.originalname,
        s3_key: fileName,
        s3_url: uploadResult.Location,
        content_type: file.mimetype,
        size: file.size,
        description: documentData.description || null,
        document_type: documentData.document_type || 'Other Documents', // Default to 'Other Documents' if not specified
        is_public: documentData.is_public || false,
        storage_provider: 'aws_s3'
      };

      const savedDocument = await Document.create(documentInfo);

      return savedDocument;
    } catch (error) {
      console.error('S3 upload error:', error);
      throw new Error('Failed to upload document');
    }
  }

  static async getDocumentUrl(s3Key) {
    try {
      const params = {
        Bucket: BUCKET_NAME,
        Key: s3Key,
        Expires: 3600 // URL expires in 1 hour
      };

      const url = await s3.getSignedUrlPromise('getObject', params);
      return url;
    } catch (error) {
      console.error('S3 get URL error:', error);
      throw new Error('Failed to generate document URL');
    }
  }

  static async deleteDocument(s3Key) {
    try {
      // Delete from S3
      const params = {
        Bucket: BUCKET_NAME,
        Key: s3Key
      };

      await s3.deleteObject(params).promise();

      // Delete from database
      await Document.deleteByS3Key(s3Key);

      return true;
    } catch (error) {
      console.error('S3 delete error:', error);
      throw new Error('Failed to delete document');
    }
  }

  static async listUserDocuments(userId, options = {}) {
    try {
      // Get documents from database
      return await Document.findByUserId(userId, options);
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to list documents');
    }
  }

  static async getDocumentById(documentId) {
    try {
      return await Document.findById(documentId);
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to get document');
    }
  }

  static async updateDocument(documentId, updateData) {
    try {
      return await Document.update(documentId, updateData);
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to update document');
    }
  }
}

module.exports = DocumentService;