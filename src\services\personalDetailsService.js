const PersonalDetails = require('../models/personalDetails');

class PersonalDetailsService {
  
  /**
   * Upsert personal details - creates new details if not exist, updates if exists
   * @param {string} userId - User ID to create or update details for
   * @param {object} detailsData - Details data to save
   * @returns {object} Created or updated personal details
   */
  static async upsertPersonalDetails(userId, detailsData) {
    try {
      // Check if personal details already exist for this user
      const existingDetails = await PersonalDetails.findByUserId(userId);
      
      // Add user_id to the details data
      const detailsWithUserId = {
        ...detailsData,
        user_id: userId
      };

      if (existingDetails) {
        // If details exist, update them
        return await PersonalDetails.personalDetailsUpdate(userId, detailsWithUserId);
      } else {
        // If details don't exist, create new ones
        return await PersonalDetails.create(detailsWithUserId);
      }
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  static async createPersonalDetails(userId, detailsData) {
    try {
      // Check if personal details already exist for this user
      const existingDetails = await PersonalDetails.findByUserId(userId);
      if (existingDetails) {
        throw new Error('Personal details already exist for this user');
      }

      // Add user_id to the details data
      const detailsWithUserId = {
        ...detailsData,
        user_id: userId
      };

      return await PersonalDetails.create(detailsWithUserId);
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  static async getPersonalDetails(userId) {
    try {
      const details = await PersonalDetails.findByUserId(userId);
      if (!details) {
        throw new Error('Personal details not found');
      }
      return details;
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  static async updatePersonalDetails(userId, updateData) {
    try {
      const updatedPersonalDetails = await PersonalDetails.personalDetailsUpdate(userId, updateData);
      if (!updatedPersonalDetails) {
        throw new Error('Personal details not found');
      }
      return updatedPersonalDetails;
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  static async updateProfessionalDetails(userId, updateData) {
    try {
      const updatedProfessionalDetails = await PersonalDetails.professionalDetailsUpdate(userId, updateData);
      if (!updatedProfessionalDetails) {
        throw new Error('Personal details not found');
      }
      return updatedProfessionalDetails;
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  static async deletePersonalDetails(userId) {
    try {
      const deleted = await PersonalDetails.delete(userId);
      if (!deleted) {
        throw new Error('Personal details not found');
      }
      return { message: 'Personal details deleted successfully' };
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }
}

module.exports = PersonalDetailsService;