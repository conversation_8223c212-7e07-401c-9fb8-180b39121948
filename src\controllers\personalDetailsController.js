const PersonalDetailsService = require('../services/personalDetailsService');

class PersonalDetailsController {
  static async createPersonalDetails(req, res) {
    
    try {
      const details = await PersonalDetailsService.upsertPersonalDetails(
        req.user.sub,
        req.body
      );
      res.status(200).json({
        message: 'Personal details created successfully',
        details
      });
    } catch (error) {
      console.error('Controller error:', error);
      if (error.message === 'Personal details already exist for this user') {
        res.status(409).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Failed to create personal details' });
      }
    }
  }

  static async getPersonalDetails(req, res) {
    try {
      const details = await PersonalDetailsService.getPersonalDetails(req.user.sub);
      res.json({ details });
    } catch (error) {
      console.error('Controller error:', error);
      if (error.message === 'Personal details not found') {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Failed to get personal details' });
      }
    }
  }

  static async updatePersonalDetails(req, res) {
    try {
      // console.log('Update payload:', req.body);      
      const details = await PersonalDetailsService.updatePersonalDetails(
        req.user.sub,
        req.body
      );
      // console.log('Update successful, returning:', details);
      res.json({
        message: 'Personal details updated successfully',
        details
      });
    } catch (error) {
      console.error('Controller error:', error);
      if (error.message === 'Personal details not found') {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Failed to update personal details' });
      }
    }
  }
  
  static async updateProfessionalDetails(req, res) {
    try {
      // console.log('Update payload:', req.body);      
      const details = await PersonalDetailsService.updateProfessionalDetails(
        req.user.sub,
        req.body
      );
      // console.log('Update successful, returning:', details);
      res.json({
        message: 'Personal details updated successfully',
        details
      });
    } catch (error) {
      console.error('Controller error:', error);
      if (error.message === 'Personal details not found') {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Failed to update personal details' });
      }
    }
  }

  static async deletePersonalDetails(req, res) {
    try {
      const result = await PersonalDetailsService.deletePersonalDetails(req.user.sub);
      res.json(result);
    } catch (error) {
      console.error('Controller error:', error);
      if (error.message === 'Personal details not found') {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Failed to delete personal details' });
      }
    }
  }
}

module.exports = PersonalDetailsController; 