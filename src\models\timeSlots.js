const { pool } = require('../config/database');

class TimeSlots {
  constructor(data) {
    this.id = data.id;
    this.time_slot = data.time_slot;
    this.isActive = data.isActive;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  static async findAll() {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM time_slots WHERE isActive = "1" ORDER BY time_slot ASC'
      );
      return rows.map(row => new TimeSlots(row));
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch time slots');
    }
  }

  static async findById(id) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM time_slots WHERE id = ? AND isActive = "1"',
        [id]
      );
      return rows.length ? new TimeSlots(rows[0]) : null;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch time slot');
    }
  }
}

module.exports = TimeSlots;