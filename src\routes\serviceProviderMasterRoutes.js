const express = require('express');
const router = express.Router();
const { authenticateToken, requireUserType } = require('../middleware/auth');
const ServiceProviderMasterController = require('../controllers/serviceProviderMasterController');

// Middleware to check for nurse user type only
const requireNurse = (req, res, next) => {
  if (req.user && req.user.userType === 'nurse') {
    return next();
  }
  return res.status(403).json({ 
    error: 'Access denied: Nurse access required',
    userType: req.user ? req.user.userType : 'unknown'
  });
};

router.use(authenticateToken);
router.use(requireUserType);
router.use(requireNurse);

router.get('/', ServiceProviderMasterController.getAllServiceProviders);
router.get('/:id', ServiceProviderMasterController.getServiceProviderById);

module.exports = router; 