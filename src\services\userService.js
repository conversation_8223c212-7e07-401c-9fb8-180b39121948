const db = require("../config/database");
const pool = db.pool;

class UserService {
  static async findByUsername(username) {
    try {
      const [rows] = await pool.query(
        "SELECT * FROM users WHERE username = ?",
        [username]
      );
      return rows.length ? rows[0] : null;
    } catch (error) {
      console.error("Database error:", error);
      throw new Error("Failed to fetch user by username");
    }
  }

  static async findByEmail(email) {
    try {
      const [rows] = await pool.query("SELECT * FROM users WHERE email = ?", [
        email,
      ]);
      return rows.length ? rows[0] : null;
    } catch (error) {
      console.error("Database error:", error);
      throw new Error("Failed to fetch user by email");
    }
  }

  static async findByPhoneNumber(phoneNumber) {
    try {
      const [rows] = await pool.query(
        "SELECT * FROM users WHERE phone_number = ?",
        [phoneNumber]
      );
      return rows.length ? rows[0] : null;
    } catch (error) {
      console.error("Database error:", error);
      throw new Error("Failed to fetch user by phone number");
    }
  }

  static async create(userData) {
    try {
      const now = new Date();
      const [result] = await pool.query(
        "INSERT INTO users (cognito_id, username, email, name, phone_number, middle_name, family_name, given_name, created_at, updated_at, is_confirmed, profile_verified) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        [
          userData.cognito_id,
          userData.username,
          userData.email,
          userData.name || null,
          userData.phone_number || null,
          userData.middle_name || null,
          userData.family_name || null,
          userData.given_name || null,
          now,
          now,
          userData.is_confirmed || false,
          userData.profile_verified || true
        ]
      );

      return { id: result.insertId, ...userData };
    } catch (error) {
      console.error("Database error:", error);
      throw new Error("Failed to create user in database");
    }
  }

  static async updateConfirmationStatus(username, status) {
    try {
      await pool.query(
        "UPDATE users SET is_confirmed = ?, updated_at = ? WHERE username = ?",
        [status, new Date(), username]
      );
      return true;
    } catch (error) {
      console.error("Database error:", error);
      throw new Error("Failed to update user confirmation status");
    }
  }

  static async findById(id) {
  try {
    const [rows] = await pool.query("SELECT * FROM users WHERE id = ?", [id]);
    return rows[0] || null;
  } catch (error) {
    throw new Error("Failed to fetch user by ID");
  }
}

  static async updateProfileVerified(usernameOrId, status) {
  try {
    const [result] = await pool.query(
      "UPDATE users SET profile_verified = ?, updated_at = ? WHERE username = ? OR id = ?",
      [status, new Date(), usernameOrId, usernameOrId]
    );
    return result.affectedRows > 0;
  } catch (error) {
    throw new Error("Failed to update profile_verified");
  }
}

  static async updateProfile(
    username,
    { given_name, family_name, phone_number }
  ) {
    try {
      const [result] = await pool.query(
        "UPDATE users SET given_name = ?, family_name = ?, phone_number = ?, updated_at = ? WHERE username = ?",
        [given_name, family_name, phone_number, new Date(), username]
      );

      if (result.affectedRows === 0) {
        return null;
      }

      return this.findByUsername(username);
    } catch (error) {
      console.error("Database error:", error);
      throw new Error("Failed to update user profile");
    }
  }

  static async deleteUser(username) {
    try {
      // First get the user's cognito_id since that's what the stored procedure needs
      const getUserQuery = "SELECT cognito_id FROM users WHERE username = ?";
      const [userRows] = await pool.query(getUserQuery, [username]);

      if (!userRows || userRows.length === 0) {
        throw new Error("User not found");
      }

      const cognitoId = userRows[0].cognito_id;

      // Call the stored procedure with cognito_id
      const [result] = await pool.query("CALL DeleteUserRecords(?)", [
        cognitoId,
      ]);

      // The stored procedure returns a success message, but we can also check if it executed
      return true;
    } catch (error) {
      console.error("Database error:", error);

      // Handle specific stored procedure errors
      if (error.code === "ER_SIGNAL_EXCEPTION") {
        throw new Error(
          "Failed to delete user records - database constraint error"
        );
      }
      throw new Error("Failed to delete user");
    }
  }

  static async listUsers({
    page = 1,
    limit = 10,
    search,
    email,
    username,
    is_confirmed,
    profile_verified,
    sort_by = "created_at",
    sort_order = "DESC",
    fields,
  }) {
    try {
      const offset = (page - 1) * limit;
      const whereConditions = [];
      const queryParams = [];

      if (search) {
        whereConditions.push(
          "(username LIKE ? OR email LIKE ? OR given_name LIKE ? OR family_name LIKE ?)"
        );
        queryParams.push(
          `%${search}%`,
          `%${search}%`,
          `%${search}%`,
          `%${search}%`
        );
      }

      if (email) {
        whereConditions.push("email = ?");
        queryParams.push(email);
      }

      if (username) {
        whereConditions.push("username = ?");
        queryParams.push(username);
      }

      if (is_confirmed !== undefined) {
        whereConditions.push("is_confirmed = ?");
        queryParams.push(is_confirmed === "true");
      }

      const allowedSortFields = [
        "username",
        "email",
        "created_at",
        "updated_at",
        "is_confirmed",
        "profile_verified",
      ];
      const sortBy = allowedSortFields.includes(sort_by)
        ? sort_by
        : "created_at";
      const sortOrder = sort_order.toUpperCase() === "ASC" ? "ASC" : "DESC";

      const whereClause =
        whereConditions.length > 0
          ? "WHERE " + whereConditions.join(" AND ")
          : "";

      let selectedFields =
        "id, username, email, given_name, family_name, phone_number, created_at, updated_at, is_confirmed, profile_verified";
      if (fields) {
        const requestedFields = fields.split(",").map((f) => f.trim());
        const validFields = requestedFields.filter(
          (f) => allowedSortFields.includes(f) || f === "id"
        );
         if (requestedFields.includes("profile_verified") && !validFields.includes("profile_verified")) {
    validFields.push("profile_verified");
  }
        if (validFields.length > 0) {
          selectedFields = validFields.join(", ");
        }
      }

      const [countResult] = await pool.query(
        `SELECT COUNT(*) as total FROM users ${whereClause}`,
        queryParams
      );
      const total = countResult[0].total;

      const [rows] = await pool.query(
        `SELECT ${selectedFields}
         FROM users
         ${whereClause}
         ORDER BY ${sortBy} ${sortOrder}
         LIMIT ? OFFSET ?`,
        [...queryParams, limit, offset]
      );

      return {
        users: rows,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
        filters: {
          search: search || null,
          email: email || null,
          username: username || null,
          is_confirmed:
            is_confirmed !== undefined ? is_confirmed === "true" : null,
        },
        sorting: {
          sortBy,
          sortOrder,
        },
      };
    } catch (error) {
      console.error("Database error:", error);
      throw new Error("Failed to fetch users");
    }
  }

  static async findById(userId) {
    try {
      const [rows] = await pool.query(
        "SELECT * FROM users WHERE cognito_id = ?",
        [userId]
      );
      return rows.length ? rows[0] : null;
    } catch (error) {
      console.error("Database error:", error);
      throw new Error("Failed to fetch user by ID");
    }
  }

  static async updateLocation(username, locationData) {
    try {
      const { latitude, longitude, address } = locationData;
      const nurse_set_location = true;
      // console.log( locationData, username);

      const [result] = await pool.query(
        "UPDATE users SET latitude = ?, longitude = ?, address = ?,nurse_set_location = ?, updated_at = ? WHERE username = ?",
        [latitude, longitude, address, nurse_set_location, new Date(), username]
      );
      // console.log("result :: ",result)
      if (result.affectedRows === 0) {
        return null;
      }

      return this.findByUsername(username);
    } catch (error) {
      console.error("Database error:", error);
      throw new Error("Failed to update user location");
    }
  }

  static async findByCognitoId(cognitoId) {
    try {
      const [rows] = await pool.query(
        "SELECT * FROM users WHERE cognito_id = ?",
        [cognitoId]
      );
      return rows.length ? rows[0] : null;
    } catch (error) {
      console.error("Database error:", error);
      throw new Error("Failed to fetch user");
    }
  }
}

module.exports = UserService;