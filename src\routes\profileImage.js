const express = require('express');
const multer = require('multer');
const { authenticateToken, requireUserType } = require('../middleware/auth');
const ProfileImageController = require('../controllers/profileImageController');

const router = express.Router();
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit for profile images
  }
});

// Middleware to check for nurse user type only
const requireNurse = (req, res, next) => {
  if (req.user && req.user.userType === 'nurse') {
    return next();
  }
  return res.status(403).json({ 
    error: 'Access denied: Nurse access required',
    userType: req.user ? req.user.userType : 'unknown'
  });
};

// Middleware to check for nurse or customer user type
const requireNurseOrCustomer = (req, res, next) => {
  if (req.user && (req.user.userType === 'nurse' || req.user.userType === 'customer')) {
    return next();
  }
  return res.status(403).json({ 
    error: 'Access denied: Nurse or Customer access required',
    userType: req.user ? req.user.userType : 'unknown'
  });
};

// All routes require authentication and valid user type
router.use(authenticateToken);
router.use(requireUserType);

// Upload a profile image - Nurse only
router.post('/upload', requireNurse, upload.single('image'), ProfileImageController.uploadProfileImage);

// Get current user's profile image - Both nurse and customer
router.get('/me', requireNurseOrCustomer, ProfileImageController.getCurrentUserProfileImage);

// Get a signed URL for downloading a profile image by S3 key - Both nurse and customer
router.get('/s3/:s3Key/url', requireNurseOrCustomer, ProfileImageController.getProfileImageUrl);

// Get profile image by user ID - Both nurse and customer
router.get('/user/:userId', requireNurseOrCustomer, ProfileImageController.getProfileImage);

// Get all nurses profile images as one object - Customer only (for browsing nurses)
router.get('/nurses/all', requireNurseOrCustomer, ProfileImageController.getAllNursesProfileImages);

// Update profile image - Nurse only
router.put('/update', requireNurse, upload.single('image'), ProfileImageController.updateProfileImage);

// Delete profile image - Nurse only
router.delete('/delete', requireNurse, ProfileImageController.deleteProfileImage);

module.exports = router;
