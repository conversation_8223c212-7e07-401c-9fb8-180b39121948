const express = require('express');
const { authenticateToken, requireUserType } = require('../middleware/auth');
const PersonalDetailsController = require('../controllers/personalDetailsController');

const router = express.Router();

// Middleware to check for specific user types
const requireNurse = (req, res, next) => {
  if (req.user && req.user.userType === 'nurse') {
    return next();
  }
  return res.status(403).json({ 
    error: 'Access denied: Nurse access required',
    userType: req.user ? req.user.userType : 'unknown'
  });
};

const requireNurseOrCustomer = (req, res, next) => {
  if (req.user && (req.user.userType === 'nurse' || req.user.userType === 'customer')) {
    return next();
  }
  return res.status(403).json({ 
    error: 'Access denied: Nurse or Customer access required',
    userType: req.user ? req.user.userType : 'unknown'
  });
};

// All routes require authentication
router.use(authenticateToken);
router.use(requireUserType);

// Create personal details - Nurse only
router.post('/create', requireNurse, PersonalDetailsController.createPersonalDetails);

// Get personal details - Nurse and Customer
router.get('/getProfile', requireNurseOrCustomer, PersonalDetailsController.getPersonalDetails);

// Update personal details - Nurse only
router.put('/updatePersonalProfile', requireNurse, PersonalDetailsController.updatePersonalDetails);

// Update professional details - Nurse only
router.put('/updateProfessionalProfile', requireNurse, PersonalDetailsController.updateProfessionalDetails);

// Delete personal details - Nurse only
router.delete('/deleteProfile', requireNurse, PersonalDetailsController.deletePersonalDetails);

module.exports = router;