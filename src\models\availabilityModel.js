const { pool } = require('../config/database');

class NurseAvailability {
  constructor(data) {
    this.id = data.id;
    this.nurse_id = data.nurse_id;
    this.availability_time = data.availability_time;
    this.availability_date = data.availability_date ? data.availability_date.toLocaleDateString('en-CA') : null;
    // Robust parsing of availability_slots to handle both legacy and new formats
    if (data.availability_slots) {
      if (typeof data.availability_slots === 'string') {
        try {
          // Try to parse as JSO<PERSON> first
          this.availability_slots = JSON.parse(data.availability_slots);
        } catch (e) {
          // If parsing fails, it's likely a legacy single time slot string
          console.log('Legacy time slot detected, converting to array format');
          this.availability_slots = [data.availability_slots];
        }
      } else if (Array.isArray(data.availability_slots)) {
        // Already an array
        this.availability_slots = data.availability_slots;
      } else {
        // Fallback to empty array
        this.availability_slots = [];
      }
    } else {
      this.availability_slots = [];
    }
    this.hourly_fare = data.hourly_fare;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  static async create(availabilityData) {
    try {
      // Convert time slots array to JSON string for storage
      const timeSlotsJson = Array.isArray(availabilityData.availability_time)
        ? JSON.stringify(availabilityData.availability_time)
        : availabilityData.availability_time;

      const [result] = await pool.query(
        'INSERT INTO nurse_availability (nurse_id, availability_date, availability_slots, hourly_fare, created_at, updated_at) VALUES (?, ?, ?, ?, CONVERT_TZ(NOW(), "UTC", "+05:30"), CONVERT_TZ(NOW(), "UTC", "+05:30"))',
        [
          availabilityData.nurse_id,
          availabilityData.availability_date,
          timeSlotsJson,
          availabilityData.hourly_fare
        ]
      );
      return { id: result.insertId, ...availabilityData };
    } catch (error) {
      console.error('Database error:', error);
      if (error.code === 'ER_DUP_ENTRY') {
        throw new Error('This availability date already exists for this nurse');
      }
      throw new Error('Failed to create nurse availability');
    }
  }

  static async findById(id, date = null) {
    try {
      let query = 'SELECT * FROM nurse_availability WHERE id = ?';
      const params = [id];

      if (date) {
        query += ' AND availability_date = ?';
        params.push(date);
      }

      query += ' ORDER BY availability_slots ASC';

      const [rows] = await pool.query(query, params);
      return rows.map(row => new NurseAvailability(row));
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch nurse availability');
    }
  }

  // New method to find all availability slots for a nurse
  static async findByNurseId(nurseId) {
    try {
      const [rows] = await pool.query(
        `SELECT * FROM nurse_availability WHERE nurse_id = ? ORDER BY availability_date, availability_slots`,
        [nurseId]
      );
      return rows.map(row => new NurseAvailability(row));
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch availability slots for the nurse');
    }
  }

  static async findAll(limit = 10, offset = 0, filters = {}) {
    try {
      let query = 'SELECT * FROM nurse_availability WHERE 1=1';
      let countQuery = 'SELECT COUNT(*) as total FROM nurse_availability WHERE 1=1';
      const params = [];
      const countParams = [];

      // Apply filters
      if (filters.nurseId) {
        query += ' AND nurse_id = ?';
        countQuery += ' AND nurse_id = ?';
        params.push(filters.nurseId);
        countParams.push(filters.nurseId);
      }

      if (filters.date) {
        query += ' AND availability_date = ?';
        countQuery += ' AND availability_date = ?';
        params.push(filters.date);
        countParams.push(filters.date);
      }

      // Order by availability_slot
      query += ' ORDER BY availability_slots ASC LIMIT ? OFFSET ?';
      params.push(limit, offset);

      const [rows] = await pool.query(query, params);
      const [countResult] = await pool.query(countQuery, countParams);

      return {
        data: rows.map(row => new NurseAvailability(row)),
        total: countResult[0].total
      };
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch availabilities');
    }
  }

  static async update(id, updateData) {
    try {
      const updateFields = [];
      const params = [];

      if (updateData.availability_slot !== undefined) {
        updateFields.push('availability_slots = ?');
        params.push(updateData.availability_slot);
      }

      if (updateData.hourly_fare !== undefined) {
        updateFields.push('hourly_fare = ?');
        params.push(updateData.hourly_fare);
      }

      if (updateFields.length === 0) {
        throw new Error('No fields to update');
      }

      updateFields.push('updated_at = CONVERT_TZ(NOW(), "UTC", "+05:30")');
      params.push(id);

      const [result] = await pool.query(
        `UPDATE nurse_availability SET ${updateFields.join(', ')} WHERE id = ?`,
        params
      );

      if (result.affectedRows === 0) {
        throw new Error('No availability found to update');
      }

      // Return updated record
      const updated = await this.findById(id);
      return updated;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to update nurse availability');
    }
  }

  static async deleteByNurseId(nurseId) {
    try {
      const [result] = await pool.query(
        'DELETE FROM nurse_availability WHERE nurse_id = ?',
        [nurseId]
      );

      if (result.affectedRows === 0) {
        throw new Error('No availability found to delete');
      }

      return result.affectedRows;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to delete nurse availability');
    }
  }

  static async deleteById(id) {
    try {
      const [result] = await pool.query(
        'DELETE FROM nurse_availability WHERE id = ?',
        [id]
      );

      if (result.affectedRows === 0) {
        throw new Error('No availability found to delete');
      }

      return result.affectedRows;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to delete nurse availability');
    }
  }

  static async findByDateAndNurse(nurseId, availabilityDate) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM nurse_availability WHERE nurse_id = ? AND availability_date = ?',
        [nurseId, availabilityDate]
      );
      if (rows.length === 0) return null;
      // Handle legacy data format conversion
      const rawData = rows[0];
      if (rawData.availability_slots && typeof rawData.availability_slots === 'string') {
        // Check if it's already valid JSON
        try {
          JSON.parse(rawData.availability_slots);
        } catch (e) {
          // If not valid JSON, treat as single time slot and convert to array
          console.log('Converting legacy time slot format to JSON array');
          const legacyTimeSlot = rawData.availability_slots;
          const jsonArray = JSON.stringify([legacyTimeSlot]);
          // Update the database record to new format
          await pool.query(
            'UPDATE nurse_availability SET availability_slots = ? WHERE id = ?',
            [jsonArray, rawData.id]
          );
          // Update the raw data for the constructor
          rawData.availability_slots = jsonArray;
        }
      }
      return new NurseAvailability(rawData);
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to find availability by date and nurse');
    }
  }

  static async updateTimeSlots(availabilityId, timeSlots, hourlyFare) {
    try {
      const timeSlotsJson = JSON.stringify(timeSlots);
      const [result] = await pool.query(
        'UPDATE nurse_availability SET availability_slots = ?, hourly_fare = ?, updated_at = CONVERT_TZ(NOW(), "UTC", "+05:30") WHERE id = ?',
        [timeSlotsJson, hourlyFare, availabilityId]
      );
      if (result.affectedRows === 0) {
        throw new Error('Availability not found');
      }
      // Return updated record
      const [rows] = await pool.query(
        'SELECT * FROM nurse_availability WHERE id = ?',
        [availabilityId]
      );
      return rows.length > 0 ? new NurseAvailability(rows[0]) : null;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to update availability time slots');
    }
  }

  static async findExactSlot(nurseId, availabilitySlot) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM nurse_availability WHERE nurse_id = ? AND availability_date = ?',
        [nurseId, availabilitySlot.availability_date]
      );
      if (rows.length === 0) {
        return null;
      }
      const availability = new NurseAvailability(rows[0]);
      // Check if the specific time slot exists in the JSON array
      const timeSlots = availability.availability_slots;
      const hasTimeSlot = Array.isArray(timeSlots)
        ? timeSlots.includes(availabilitySlot.availability_time)
        : timeSlots === availabilitySlot.availability_time;
      return hasTimeSlot ? availability : null;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to check for exact availability slot');
    }
  }
}

module.exports = NurseAvailability;
