const express = require('express');
const router = express.Router();
const { authenticateToken, requireUserType } = require('../middleware/auth');
const TimeSlotsController = require('../controllers/timeSlotsController');

// Middleware to check for specific user types
const requireNurse = (req, res, next) => {
  if (req.user && req.user.userType === 'nurse') {
    return next();
  }
  return res.status(403).json({ 
    error: 'Access denied: Nurse access required',
    userType: req.user ? req.user.userType : 'unknown'
  });
};

router.use(authenticateToken);
router.use(requireUserType);
router.use(requireNurse)

router.get('/', TimeSlotsController.getAllTimeSlots);
router.get('/:id', TimeSlotsController.getTimeSlotById);

module.exports = router;