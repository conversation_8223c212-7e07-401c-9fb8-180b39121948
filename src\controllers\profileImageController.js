const ProfileImageService = require("../services/profileImageService");

class ProfileImageController {
  static async uploadProfileImage(req, res) {
    try {
      if (!req.file) {
        return res.status(400).json({ error: "No image file uploaded" });
      }

      // Upload profile image and save to database
      const result = await ProfileImageService.uploadProfileImage(
        req.user.sub,
        req.file
      );

      // Generate a signed URL for immediate access
      const profileImageData =
        await ProfileImageService.getProfileImageByUserId(req.user.sub);

      res.status(201).json({
        message: "Profile image uploaded successfully to AWS S3",
        profile_image: {
          profile_image_name: result.profile_image_name,
          s3_key: result.s3_key,
          s3_url: result.s3_url,
          signed_url: profileImageData.signed_url,
          updated_at: result.updated_at,
        },
      });
    } catch (error) {
      console.error("Upload error:", error);
      res
        .status(500)
        .json({ error: `Failed to upload profile image: ${error.message}` });
    }
  }

  static async getProfileImage(req, res) {
    try {
      const { userId } = req.params;

      // If no userId provided, use the authenticated user's ID
      const targetUserId = userId || req.user.sub;

      const profileImage = await ProfileImageService.getProfileImageByUserId(
        targetUserId
      );

      if (!profileImage) {
        return res.json({
          message: "No profile image found for this user",
          profile_image: null,
        });
      }

      res.json({
        message: "Profile image retrieved successfully",
        profile_image: profileImage,
      });
    } catch (error) {
      console.error("Get profile image error:", error);
      res.status(500).json({ error: "Failed to get profile image" });
    }
  }

  static async getProfileImageUrl(req, res) {
    try {
      const { s3Key } = req.params;
      const url = await ProfileImageService.getProfileImageUrl(s3Key);
      res.json({ url });
    } catch (error) {
      console.error("Get URL error:", error);
      res.status(500).json({ error: "Failed to generate profile image URL" });
    }
  }

  static async updateProfileImage(req, res) {
    try {
      if (!req.file) {
        return res.status(400).json({ error: "No image file uploaded" });
      }

      // Update profile image
      const result = await ProfileImageService.updateProfileImage(
        req.user.sub,
        req.file
      );

      // Generate a signed URL for immediate access
      const profileImageData =
        await ProfileImageService.getProfileImageByUserId(req.user.sub);

      res.json({
        message: "Profile image updated successfully",
        profile_image: {
          profile_image_name: result.profile_image_name,
          s3_key: result.s3_key,
          s3_url: result.s3_url,
          signed_url: profileImageData.signed_url,
          updated_at: result.updated_at,
        },
      });
    } catch (error) {
      console.error("Update error:", error);
      res
        .status(500)
        .json({ error: `Failed to update profile image: ${error.message}` });
    }
  }

  static async deleteProfileImage(req, res) {
    try {
      await ProfileImageService.deleteProfileImage(req.user.sub);
      res.json({ message: "Profile image deleted successfully" });
    } catch (error) {
      console.error("Delete error:", error);
      res
        .status(500)
        .json({ error: `Failed to delete profile image: ${error.message}` });
    }
  }

  static async getCurrentUserProfileImage(req, res) {
    try {
      const profileImage = await ProfileImageService.getProfileImageByUserId(
        req.user.sub
      );

      if (!profileImage) {
        return res.json({
          message: "No profile image found",
          profile_image: null,
        });
      }

      res.json({
        message: "Profile image retrieved successfully",
        profile_image: profileImage,
      });
    } catch (error) {
      console.error("Get current user profile image error:", error);
      res.status(500).json({ error: "Failed to get profile image" });
    }
  }
}

module.exports = ProfileImageController;
