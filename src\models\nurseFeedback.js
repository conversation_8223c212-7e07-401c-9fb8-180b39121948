const { pool } = require('../config/database');

class NurseFeedback {
  constructor(data) {
    this.id = data.id;
    this.name = data.name;
    this.nurseId = data.nurseId;
    this.feedbackId = data.feedbackId;
    this.rating = data.rating;
    this.recipientName = data.recipientName;
    this.comments = data.comments;
    this.feedbackId = data.feedbackId;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  static async create(feedbackData) {
    try {
      const [result] = await pool.query(
        'INSERT INTO nurse_ratings (name, nurseId, feedbackId, rating, recipientName, comments, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, CONVERT_TZ(NOW(), "UTC", "+05:30"), CONVERT_TZ(NOW(), "UTC", "+05:30"))',
        [
          feedbackData.name,
          feedbackData.nurseId,
          feedbackData.feedbackId,
          feedbackData.rating,
          feedbackData.recipientName,
          feedbackData.comments,
          feedbackData.feedbackId
        ]
      );

      return { id: result.insertId, ...feedbackData };
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to create nurse feedback');
    }
  }

  static async findByNurseId(nurseId) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM nurse_ratings WHERE nurseId = ? ORDER BY created_at DESC',
        [nurseId]
      );
      return rows.map(row => new NurseFeedback(row));
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch nurse feedback');
    }
  }

  static async getAverageRating(nurseId) {
    try {
      const [rows] = await pool.query(
        'SELECT AVG(rating) as average_rating, COUNT(*) as total_feedback FROM nurse_ratings WHERE nurseId = ?',
        [nurseId]
      );
      return rows[0];
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch nurse average rating');
    }
  }
}

module.exports = NurseFeedback; 