const express = require('express');
const authRoutes = require('./auth');
const userRoutes = require('./users');
const documentRoutes = require('./documents');
const healthRoutes = require('./health');

// const router = express.Router();

// router.use('/auth', authRoutes);
// router.use('/users', userRoutes);
// router.use('/documents', documentRoutes);
// router.use('/health', healthRoutes);

module.exports = {
  authRoutes,
  userRoutes,
  healthRoutes,
  documentRoutes
}; 