const { pool } = require('../config/database');

class Document {
  constructor(data) {
    this.id = data.id;
    this.user_id = data.user_id;
    this.file_name = data.file_name;
    this.original_name = data.original_name;
    this.s3_key = data.s3_key;
    this.s3_url = data.s3_url;
    this.content_type = data.content_type;
    this.size = data.size;
    this.description = data.description;
    this.document_type = data.document_type;
    this.is_public = data.is_public || false;
    this.storage_provider = data.storage_provider || 'aws_s3';
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  static async create(documentData) {
    try {
      const [result] = await pool.query(
        `INSERT INTO documents
        (user_id, file_name, original_name, s3_key, s3_url, content_type, size, description, document_type, is_public, storage_provider)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          documentData.user_id,
          documentData.file_name,
          documentData.original_name,
          documentData.s3_key,
          documentData.s3_url,
          documentData.content_type,
          documentData.size,
          documentData.description || null,
          documentData.document_type || null,
          documentData.is_public || false,
          documentData.storage_provider || 'aws_s3'
        ]
      );

      return { id: result.insertId, ...documentData };
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to create document in database');
    }
  }

  static async findById(id) {
    try {
      const [rows] = await pool.query('SELECT * FROM documents WHERE id = ?', [id]);
      return rows.length ? new Document(rows[0]) : null;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch document by ID');
    }
  }

  static async findByS3Key(s3Key) {
    try {
      const [rows] = await pool.query('SELECT * FROM documents WHERE s3_key = ?', [s3Key]);
      return rows.length ? new Document(rows[0]) : null;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch document by S3 key');
    }
  }

  static async findByUserId(userId, options = {}) {
    try {
      const { page = 1, limit = 10, documentType } = options;
      const offset = (page - 1) * limit;

      let query = 'SELECT * FROM documents WHERE user_id = ?';
      const queryParams = [userId];

      if (documentType) {
        // Handle exact document type matching
        query += ' AND document_type = ?';
        queryParams.push(documentType);
      }

      query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
      queryParams.push(limit, offset);

      const [rows] = await pool.query(query, queryParams);

      // Get total count for pagination
      let countQuery = 'SELECT COUNT(*) as total FROM documents WHERE user_id = ?';
      const countParams = [userId];

      if (documentType) {
        countQuery += ' AND document_type = ?';
        countParams.push(documentType);
      }

      const [countResult] = await pool.query(countQuery, countParams);
      const total = countResult[0].total;

      return {
        documents: rows.map(row => new Document(row)),
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch documents by user ID');
    }
  }

  static async update(id, updateData) {
    try {
      const allowedFields = ['description', 'document_type', 'is_public'];
      const updates = [];
      const values = [];

      // Validate document_type if provided
      if (updateData.document_type) {
        const validDocumentTypes = ['ID Proof', 'Experience Proof', 'Other Documents'];
        if (!validDocumentTypes.includes(updateData.document_type)) {
          throw new Error('Invalid document_type. Must be one of: ID Proof, Experience Proof, Other Documents');
        }
      }

      for (const [key, value] of Object.entries(updateData)) {
        if (allowedFields.includes(key)) {
          updates.push(`${key} = ?`);
          values.push(value);
        }
      }

      if (updates.length === 0) {
        return null;
      }

      values.push(id);

      const [result] = await pool.query(
        `UPDATE documents SET ${updates.join(', ')}, updated_at = CONVERT_TZ(NOW(), "UTC", "+05:30") WHERE id = ?`,
        values
      );

      if (result.affectedRows === 0) {
        return null;
      }

      return this.findById(id);
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to update document');
    }
  }

  static async delete(id) {
    try {
      const [result] = await pool.query('DELETE FROM documents WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to delete document');
    }
  }

  static async deleteByS3Key(s3Key) {
    try {
      const [result] = await pool.query('DELETE FROM documents WHERE s3_key = ?', [s3Key]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to delete document by S3 key');
    }
  }
}

module.exports = Document;