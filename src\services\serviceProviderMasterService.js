const ServiceProviderMaster = require('../models/serviceProviderMaster');

class ServiceProviderMasterService {
  static async getAllServiceProviders() {
    try {
      return await ServiceProviderMaster.findAll();
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  static async getServiceProviderById(id) {
    try {
      const provider = await ServiceProviderMaster.findById(id);
      if (!provider) {
        throw new Error('Service provider not found');
      }
      return provider;
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }
}

module.exports = ServiceProviderMasterService; 